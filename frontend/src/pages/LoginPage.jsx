import { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
// Remove GoogleLoginButton import - using inline custom implementation
import { FaDiscord } from 'react-icons/fa';
import axios from 'axios';
import { googleLogin } from '../services/googleAuthService';
import { getDiscordOAuthUrl } from '../services/discordAuthService';
import { API_URL } from '../config/env.js';

const LoginPage = () => {
  const { login, user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const from = location.state?.from?.pathname || '/';
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  
  const [isLoadingForm, setIsLoadingForm] = useState(false);
  const [formError, setFormError] = useState('');
  
  // Redirect if user is already logged in
  useEffect(() => {
    if (user) {
      navigate('/');
    }
  }, [user, navigate]);

  // Test server connection on component mount
  useEffect(() => {
    const testServerConnection = async () => {
      try {
        // Use a simple GET request to the health endpoint
        await axios.get(`${API_URL}/health`);
  
      } catch (err) {
        console.warn('Server connection test failed:', err.message);
        // Don't set error here, just log for debugging
      }
    };
    
    testServerConnection();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoadingForm(true);
    setFormError('');
    
    if (!formData.email || !formData.password) {
      setFormError('Please fill in all fields');
      setIsLoadingForm(false);
      return;
    }
    
    try {

      
      // Use the context login function directly with credentials
      await login({
        email: formData.email,
        password: formData.password,
        rememberMe: formData.rememberMe || false
      });
      
      
      navigate(from);
    } catch (error) {
      console.error('❌ Login submission error:', error);
      setFormError(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoadingForm(false);
    }
  };

  const handleGoogleLogin = async () => {
    try {
      setFormError(''); // Clear any previous errors
      
      // Check if Google API is loaded
      if (!window.google || !window.google.accounts) {
        console.error('Google API not loaded');
        setFormError('Google API not loaded. Please try again later.');
        return;
      }


      const auth2 = window.google.accounts.oauth2;
      
      // Use token flow for all environments to simplify debugging
      const tokenClient = auth2.initTokenClient({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
        scope: 'email profile',
        callback: async (tokenResponse) => {
          if (tokenResponse.access_token) {
  
            try {
              // Pass the access token to our service
              const result = await googleLogin(tokenResponse.access_token);
              
              
              // Set the user directly without making another API call
              // We're already authenticated via cookies
              
              // Use setUser from context directly instead of calling login again
              if (result.user) {
                // This will update the context without making another API call
                login(result.user, true, true); // Added third parameter to skip API call
              }
              
              // Navigate to home page
              navigate('/');
            } catch (error) {
              console.error('Google login error:', error);
              setFormError(error.message || 'Google login failed');
            }
          }
        },
        error_callback: (error) => {
          console.error('Google auth error:', error);
          setFormError('Google authentication failed. Please try again.');
        }
      });
      
      // Request token directly

      tokenClient.requestAccessToken();
      
    } catch (error) {
      console.error('Google login exception:', error);
      setFormError('Google login failed. Please try again.');
    }
  };

  const handleDiscordLogin = () => {
    try {
      setFormError(''); // Clear any previous errors

      // Get Discord OAuth URL and redirect user
      const authUrl = getDiscordOAuthUrl();
      console.log('Discord OAuth URL:', authUrl);
      console.log('Redirecting to Discord...');
      window.location.href = authUrl;
    } catch (error) {
      console.error('Discord login error:', error);
      setFormError('Discord login failed. Please try again.');
    }
  };

  // Loading state removed since it's not being used

  return (
    <div className="flex justify-center items-center w-full min-h-screen p-8 bg-gray-900 overflow-x-hidden">
      <div className="flex flex-col w-full max-w-lg p-8 bg-gray-800 rounded-xl shadow-2xl">
        <div className="w-full">
          <h1 className="text-3xl font-bold mb-2 text-white text-center">Welcome Back</h1>
          <p className="text-gray-300 mb-6 text-center">Log in to discover and track your favorite indie games</p>
          
          {formError && (
            <div className="bg-red-500/10 text-red-400 border border-red-500/30 px-4 py-3 rounded-lg mb-4 text-sm">
              {formError}
            </div>
          )}
          
          <form className="space-y-5" onSubmit={handleSubmit}>
            <div>
              <label htmlFor="email" className="block mb-2 text-sm text-gray-300">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div>
              <label htmlFor="password" className="block mb-2 text-sm text-gray-300">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                placeholder="Enter your password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-3 bg-white border border-gray-600 rounded-lg text-black text-base transition-all duration-300 focus:outline-none focus:border-red-500 focus:ring-2 focus:ring-red-500/20"
              />
            </div>
            
            <div className="flex justify-end">
              <Link to="/forgot-password" className="text-red-500 hover:text-orange-500 transition-colors duration-300 text-sm no-underline">
                Forgot Password?
              </Link>
            </div>
            
            <button
              type="submit"
              className="w-full px-4 py-3 bg-gradient-to-r from-red-500 to-orange-500 text-white border-none rounded-full text-base font-bold cursor-pointer transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-red-500/30 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-2"
              disabled={isLoadingForm}
            >
              {isLoadingForm ? (
                <>
                  <LoadingSpinner size="sm" color="white" />
                  <span>Signing in...</span>
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>
          
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-800 text-gray-400">OR</span>
            </div>
          </div>
          
          <div className="space-y-3">
            <button 
              className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-white text-gray-700 border border-gray-300 rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 hover:shadow-md"
              onClick={handleGoogleLogin}
            >
              <img
                src="https://developers.google.com/static/identity/images/g-logo.png"
                alt="Google G"
                className="w-5 h-5"
              />
              Continue with Google
            </button>
            <button 
              className="w-full flex items-center justify-center gap-3 px-4 py-3 bg-indigo-600 text-white rounded-lg font-medium transition-all duration-300 hover:bg-indigo-700 hover:shadow-md"
              onClick={handleDiscordLogin}
            >
              <FaDiscord className="w-5 h-5" /> Continue with Discord
            </button>
          </div>
          
          <div className="mt-6 text-center text-gray-400 text-sm">
            <p>Don&apos;t have an account? <Link to="/register" className="text-red-500 hover:text-orange-500 transition-colors duration-300 no-underline">Sign Up</Link></p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
